[gd_scene load_steps=2 format=3]

[ext_resource type="Script" path="res://scripts/JumpEffect.gd" id="1"]

[node name="JumpEffect" type="Node2D"]
script = ExtResource("1")

[node name="CPUParticles2D" type="CPUParticles2D" parent="."]
emitting = false
amount = 10
lifetime = 0.5
one_shot = true
explosiveness = 1.0
direction = Vector2(0, 1)
spread = 30.0
initial_velocity_min = 30.0
initial_velocity_max = 60.0
gravity = Vector2(0, 98)
scale_amount_min = 0.3
scale_amount_max = 0.8
color = Color(0.8, 0.8, 0.8, 1)
