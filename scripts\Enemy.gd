extends CharacterBody2D

const SPEED = 50.0
var direction = 1
var gravity = 980
var patrol_distance = 100.0
var start_position

func _ready():
	start_position = position.x

func _physics_process(delta):
	# إضافة الجاذبية
	if not is_on_floor():
		velocity.y += gravity * delta
	else:
		velocity.y = 0

	# حركة الدورية
	velocity.x = direction * SPEED
	
	# تغيير الاتجاه عند الوصول لحدود الدورية
	if abs(position.x - start_position) >= patrol_distance:
		direction *= -1
		scale.x *= -1  # قلب الشكل

	move_and_slide()

func _on_area_2d_body_entered(body):
	if body.name == "Player":
		print("Player hit by enemy!")
		# إعادة اللاعب للموقع الأولي
		body.position = Vector2(100, 500)
		body.velocity = Vector2.ZERO
		
		# تقليل النقاط
		var main_scene = get_tree().current_scene
		if main_scene.has_method("lose_points"):
			main_scene.lose_points(5)
