[gd_scene load_steps=3 format=3]

[ext_resource type="Script" path="res://scripts/CoinEffect.gd" id="1"]

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_1"]
direction = Vector3(0, -1, 0)
initial_velocity_min = 50.0
initial_velocity_max = 100.0
gravity = Vector3(0, 98, 0)
scale_min = 0.5
scale_max = 1.5

[node name="CoinEffect" type="Node2D"]
script = ExtResource("1")

[node name="CPUParticles2D" type="CPUParticles2D" parent="."]
emitting = false
amount = 20
lifetime = 1.0
one_shot = true
explosiveness = 1.0
direction = Vector2(0, -1)
spread = 45.0
initial_velocity_min = 50.0
initial_velocity_max = 100.0
gravity = Vector2(0, 98)
scale_amount_min = 0.5
scale_amount_max = 1.5
color = Color(1, 1, 0, 1)

[node name="ScorePopup" type="Label" parent="."]
offset_left = -20.0
offset_top = -30.0
offset_right = 20.0
offset_bottom = -10.0
text = "+10"
horizontal_alignment = 1
vertical_alignment = 1
