# دليل استكشاف الأخطاء - Troubleshooting Guide

## المشكلة: اللعبة لا تعمل عند الضغط على F5

### الحلول المقترحة:

#### 1. التأكد من تثبيت Godot
- تأكد من تثبيت محرك Godot (الإصدار 4.1 أو أحدث)
- يمكن تحميله من: https://godotengine.org/download

#### 2. فتح المشروع في Godot
1. افتح محرك Godot
2. اضغط على "Import"
3. اختر مجلد المشروع الذي يحتوي على ملف `project.godot`
4. اضغط "Import & Edit"

#### 3. تشغيل اللعبة
- اضغط F5 أو زر "Play" في أعلى الشاشة
- إذا طُلب منك اختيار المشهد الرئيسي، اختر `scenes/Main.tscn`

#### 4. إذا ظهرت أخطاء:

**خطأ في السكريبت:**
- تأكد من أن جميع ملفات `.gd` موجودة في مجلد `scripts/`
- تحقق من عدم وجود أخطاء إملائية في أسماء الملفات

**خطأ في المشاهد:**
- تأكد من أن جميع ملفات `.tscn` موجودة في مجلد `scenes/`
- تحقق من أن المسارات في ملف `project.godot` صحيحة

**خطأ في التحكم:**
- تأكد من أن إعدادات Input Map صحيحة في `project.godot`
- يمكنك إعادة تعيين التحكم من Project Settings > Input Map

#### 5. إعادة إنشاء المشروع
إذا لم تعمل الحلول السابقة:
1. أنشئ مشروع Godot جديد
2. انسخ الملفات من مجلدات `scripts/` و `scenes/`
3. أعد إعداد المشهد الرئيسي يدوياً

#### 6. التحقق من إصدار Godot
- هذا المشروع مصمم للعمل مع Godot 4.1+
- إذا كنت تستخدم إصداراً أقدم، قد تحتاج لتحديث Godot

#### 7. التحقق من وحدة التحكم
- افتح نافذة Output في Godot لرؤية رسائل الخطأ
- ابحث عن رسائل الخطأ الحمراء وحاول حلها

### رسائل الخطأ الشائعة:

**"Invalid call. Nonexistent function"**
- تحقق من أسماء الدوال في السكريبتات

**"Scene file not found"**
- تأكد من وجود جميع ملفات المشاهد

**"Script class not found"**
- تأكد من أن السكريبتات مرتبطة بالعقد الصحيحة

### للمساعدة الإضافية:
- راجع وثائق Godot: https://docs.godotengine.org/
- منتدى Godot: https://godotengine.org/community
- Discord Godot: https://discord.gg/godotengine
