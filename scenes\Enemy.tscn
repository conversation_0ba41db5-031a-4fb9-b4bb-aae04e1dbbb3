[gd_scene load_steps=3 format=3]

[ext_resource type="Script" path="res://scripts/Enemy.gd" id="1"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(24, 24)

[node name="Enemy" type="CharacterBody2D"]
script = ExtResource("1")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_1")

[node name="ColorRect" type="ColorRect" parent="."]
offset_left = -12.0
offset_top = -12.0
offset_right = 12.0
offset_bottom = 12.0
color = Color(1, 0.2, 0.2, 1)

[node name="Eyes" type="ColorRect" parent="."]
offset_left = -8.0
offset_top = -8.0
offset_right = -4.0
offset_bottom = -4.0
color = Color(1, 1, 1, 1)

[node name="Eyes2" type="ColorRect" parent="."]
offset_left = 4.0
offset_top = -8.0
offset_right = 8.0
offset_bottom = -4.0
color = Color(1, 1, 1, 1)

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
shape = SubResource("RectangleShape2D_1")

[connection signal="body_entered" from="Area2D" to="." method="_on_area_2d_body_entered"]
