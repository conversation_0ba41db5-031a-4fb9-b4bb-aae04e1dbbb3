extends Node

@onready var jump_sound = $JumpSound
@onready var coin_sound = $CoinSound
@onready var hit_sound = $HitSound
@onready var bg_music = $BGMusic

func _ready():
	# تشغيل الموسيقى الخلفية
	if bg_music:
		bg_music.autoplay = true
		bg_music.stream_paused = false

func play_jump_sound():
	if jump_sound:
		jump_sound.play()
	print("Jump sound!")

func play_coin_sound():
	if coin_sound:
		coin_sound.play()
	print("Coin sound!")

func play_hit_sound():
	if hit_sound:
		hit_sound.play()
	print("Hit sound!")

func play_bg_music():
	if bg_music and not bg_music.playing:
		bg_music.play()

func stop_bg_music():
	if bg_music and bg_music.playing:
		bg_music.stop()
