extends Control

@onready var instructions_panel = $InstructionsPanel

func _ready():
	print("Main Menu loaded!")

func _on_start_button_pressed():
	print("Starting game...")
	get_tree().change_scene_to_file("res://scenes/Level1.tscn")

func _on_instructions_button_pressed():
	instructions_panel.visible = true

func _on_back_button_pressed():
	instructions_panel.visible = false

func _on_quit_button_pressed():
	print("Quitting game...")
	get_tree().quit()
