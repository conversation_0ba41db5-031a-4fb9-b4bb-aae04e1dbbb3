[gd_scene load_steps=3 format=3]

[ext_resource type="Script" path="res://scripts/Main.gd" id="1"]
[ext_resource type="PackedScene" path="res://scenes/Player.tscn" id="2"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(1024, 40)

[node name="Main" type="Node2D"]
script = ExtResource("1")

[node name="Background" type="ColorRect" parent="."]
offset_right = 1024.0
offset_bottom = 600.0
color = Color(0.529412, 0.808824, 0.921569, 1)

[node name="Ground" type="StaticBody2D" parent="."]
position = Vector2(512, 580)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Ground"]
shape = SubResource("RectangleShape2D_1")

[node name="ColorRect" type="ColorRect" parent="Ground"]
offset_left = -512.0
offset_top = -20.0
offset_right = 512.0
offset_bottom = 20.0
color = Color(0.133333, 0.545098, 0.133333, 1)

[node name="Player" parent="." instance=ExtResource("2")]
position = Vector2(100, 500)

[node name="Camera2D" type="Camera2D" parent="."]
position = Vector2(512, 300)
