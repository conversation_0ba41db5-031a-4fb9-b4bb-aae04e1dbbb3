extends Area2D

var rotation_speed = 2.0
var bounce_height = 5.0
var bounce_speed = 3.0
var original_y

func _ready():
	original_y = position.y

func _process(delta):
	# تدوير العملة
	rotation += rotation_speed * delta
	
	# حركة ارتداد بسيطة
	position.y = original_y + sin(Time.get_time() * bounce_speed) * bounce_height

func _on_body_entered(body):
	if body.name == "Player":
		# تشغيل المؤثرات
		show_coin_effect()

		# تشغيل صوت جمع العملة
		collect_coin()

		# إخفاء العملة
		visible = false
		set_collision_layer_value(1, false)
		set_collision_mask_value(1, false)

		# حذف العملة بعد المؤثرات
		await get_tree().create_timer(0.5).timeout
		queue_free()

func collect_coin():
	# إرسال إشارة إلى المشهد الرئيسي
	var main_scene = get_tree().current_scene
	if main_scene.has_method("collect_coin"):
		main_scene.collect_coin()

	print("Coin collected!")

func show_coin_effect():
	# إنشاء مؤثر جمع العملة
	var effect_scene = preload("res://scenes/CoinEffect.tscn")
	var effect = effect_scene.instantiate()
	get_parent().add_child(effect)
	effect.global_position = global_position
