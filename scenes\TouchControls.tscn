[gd_scene load_steps=2 format=3]

[ext_resource type="Script" path="res://scripts/TouchControls.gd" id="1"]

[node name="TouchControls" type="CanvasLayer"]
script = ExtResource("1")

[node name="LeftButton" type="TouchScreenButton" parent="."]
position = Vector2(50, 450)
scale = Vector2(2, 2)
texture_normal = null

[node name="ColorRect" type="ColorRect" parent="LeftButton"]
offset_left = -25.0
offset_top = -25.0
offset_right = 25.0
offset_bottom = 25.0
color = Color(1, 1, 1, 0.3)

[node name="Label" type="Label" parent="LeftButton"]
offset_left = -15.0
offset_top = -12.0
offset_right = 15.0
offset_bottom = 12.0
text = "←"
horizontal_alignment = 1
vertical_alignment = 1

[node name="RightButton" type="TouchScreenButton" parent="."]
position = Vector2(150, 450)
scale = Vector2(2, 2)

[node name="ColorRect" type="ColorRect" parent="RightButton"]
offset_left = -25.0
offset_top = -25.0
offset_right = 25.0
offset_bottom = 25.0
color = Color(1, 1, 1, 0.3)

[node name="Label" type="Label" parent="RightButton"]
offset_left = -15.0
offset_top = -12.0
offset_right = 15.0
offset_bottom = 12.0
text = "→"
horizontal_alignment = 1
vertical_alignment = 1

[node name="JumpButton" type="TouchScreenButton" parent="."]
position = Vector2(900, 450)
scale = Vector2(2, 2)

[node name="ColorRect" type="ColorRect" parent="JumpButton"]
offset_left = -30.0
offset_top = -30.0
offset_right = 30.0
offset_bottom = 30.0
color = Color(1, 1, 0, 0.3)

[node name="Label" type="Label" parent="JumpButton"]
offset_left = -20.0
offset_top = -12.0
offset_right = 20.0
offset_bottom = 12.0
text = "JUMP"
horizontal_alignment = 1
vertical_alignment = 1

[connection signal="pressed" from="LeftButton" to="." method="_on_left_button_pressed"]
[connection signal="released" from="LeftButton" to="." method="_on_left_button_released"]
[connection signal="pressed" from="RightButton" to="." method="_on_right_button_pressed"]
[connection signal="released" from="RightButton" to="." method="_on_right_button_released"]
[connection signal="pressed" from="JumpButton" to="." method="_on_jump_button_pressed"]
