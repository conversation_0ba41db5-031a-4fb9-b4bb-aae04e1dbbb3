[gd_scene load_steps=2 format=3]

[ext_resource type="Script" path="res://scripts/MainMenu.gd" id="1"]

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.2, 0.3, 0.8, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -150.0
offset_top = -100.0
offset_right = 150.0
offset_bottom = 100.0

[node name="Title" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "My First Game"
horizontal_alignment = 1

[node name="Subtitle" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "لعبة المنصات البسيطة"
horizontal_alignment = 1

[node name="HighScore" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "High Score: 0"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="StartButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "Start Game - ابدأ اللعبة"

[node name="InstructionsButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "Instructions - التعليمات"

[node name="QuitButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "Quit - خروج"

[node name="InstructionsPanel" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0

[node name="VBoxContainer" type="VBoxContainer" parent="InstructionsPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0

[node name="Title" type="Label" parent="InstructionsPanel/VBoxContainer"]
layout_mode = 2
text = "Instructions - التعليمات"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="InstructionsPanel/VBoxContainer"]
layout_mode = 2

[node name="Instructions" type="Label" parent="InstructionsPanel/VBoxContainer"]
layout_mode = 2
text = "A/D or Arrow Keys: Move
Space/Up Arrow: Jump
Collect all coins to win!

A/D أو الأسهم: الحركة
مسطرة المسافة/السهم العلوي: القفز
اجمع جميع العملات للفوز!"
horizontal_alignment = 1

[node name="BackButton" type="Button" parent="InstructionsPanel/VBoxContainer"]
layout_mode = 2
text = "Back - رجوع"

[connection signal="pressed" from="VBoxContainer/StartButton" to="." method="_on_start_button_pressed"]
[connection signal="pressed" from="VBoxContainer/InstructionsButton" to="." method="_on_instructions_button_pressed"]
[connection signal="pressed" from="VBoxContainer/QuitButton" to="." method="_on_quit_button_pressed"]
[connection signal="pressed" from="InstructionsPanel/VBoxContainer/BackButton" to="." method="_on_back_button_pressed"]
