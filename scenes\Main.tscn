[gd_scene load_steps=6 format=3 uid="uid://bxvbxvbxvbxvb"]

[ext_resource type="Script" path="res://scripts/Main.gd" id="1_0hdqp"]
[ext_resource type="PackedScene" uid="uid://cxvbxvbxvbxvb" path="res://scenes/Player.tscn" id="2_1hdqp"]
[ext_resource type="PackedScene" uid="uid://dxvbxvbxvbxvb" path="res://scenes/Coin.tscn" id="3_1hdqp"]
[ext_resource type="PackedScene" uid="uid://3hgdtepb324j" path="res://scenes/UI.tscn" id="4_1hdqp"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1hdqp"]
size = Vector2(100, 20)

[node name="Main" type="Node2D"]
script = ExtResource("1_0hdqp")

[node name="Background" type="ColorRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = 1024.0
offset_bottom = 600.0
color = Color(0.529412, 0.808824, 0.921569, 1)

[node name="Ground" type="StaticBody2D" parent="."]
position = Vector2(512, 580)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Ground"]
scale = Vector2(10.24, 1)
shape = SubResource("RectangleShape2D_1hdqp")

[node name="ColorRect" type="ColorRect" parent="Ground"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -512.0
offset_top = -10.0
offset_right = 512.0
offset_bottom = 10.0
color = Color(0.133333, 0.545098, 0.133333, 1)

[node name="Platform1" type="StaticBody2D" parent="."]
position = Vector2(200, 450)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Platform1"]
scale = Vector2(2, 1)
shape = SubResource("RectangleShape2D_1hdqp")

[node name="ColorRect" type="ColorRect" parent="Platform1"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -10.0
offset_right = 100.0
offset_bottom = 10.0
color = Color(0.545098, 0.270588, 0.0745098, 1)

[node name="Platform2" type="StaticBody2D" parent="."]
position = Vector2(600, 350)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Platform2"]
scale = Vector2(2, 1)
shape = SubResource("RectangleShape2D_1hdqp")

[node name="ColorRect" type="ColorRect" parent="Platform2"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -10.0
offset_right = 100.0
offset_bottom = 10.0
color = Color(0.545098, 0.270588, 0.0745098, 1)

[node name="Platform3" type="StaticBody2D" parent="."]
position = Vector2(800, 250)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Platform3"]
scale = Vector2(1.5, 1)
shape = SubResource("RectangleShape2D_1hdqp")

[node name="ColorRect" type="ColorRect" parent="Platform3"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -75.0
offset_top = -10.0
offset_right = 75.0
offset_bottom = 10.0
color = Color(0.545098, 0.270588, 0.0745098, 1)

[node name="Player" parent="." instance=ExtResource("2_1hdqp")]
position = Vector2(100, 500)

[node name="Camera2D" type="Camera2D" parent="."]
position = Vector2(512, 300)

[node name="Coins" type="Node2D" parent="."]

[node name="Coin1" parent="Coins" instance=ExtResource("3_1hdqp")]
position = Vector2(300, 400)

[node name="Coin2" parent="Coins" instance=ExtResource("3_1hdqp")]
position = Vector2(500, 300)

[node name="Coin3" parent="Coins" instance=ExtResource("3_1hdqp")]
position = Vector2(700, 200)

[node name="Coin4" parent="Coins" instance=ExtResource("3_1hdqp")]
position = Vector2(150, 520)

[node name="Coin5" parent="Coins" instance=ExtResource("3_1hdqp")]
position = Vector2(850, 200)

[node name="UI" parent="." instance=ExtResource("4_1hdqp")]
