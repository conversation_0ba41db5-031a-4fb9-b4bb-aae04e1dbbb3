[folding]

node_unfolds=[NodePath("."), PackedStringArray("Transform"), NodePath("Background"), PackedStringArray("Layout"), NodePath("Ground"), PackedStringArray("Transform"), NodePath("Ground/CollisionShape2D"), PackedStringArray("Transform"), NodePath("Ground/ColorRect"), PackedStringArray("Layout"), NodePath("Player"), PackedStringArray("Transform"), NodePath("Camera2D"), PackedStringArray("Transform")]
resource_unfolds=["res://scenes/Main.tscn::RectangleShape2D_1", PackedStringArray(), "res://scenes/Main.tscn::RectangleShape2D_2", PackedStringArray()]
nodes_folded=[]
