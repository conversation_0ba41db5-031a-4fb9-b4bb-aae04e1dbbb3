extends CharacterBody2D

const SPEED = 200.0
const JUMP_VELOCITY = -400.0

# Get the gravity from the project settings to be synced with RigidBody nodes.
var gravity = ProjectSettings.get_setting("physics/2d/default_gravity")
var is_jumping = false

func _physics_process(delta):
	# Add the gravity.
	if not is_on_floor():
		velocity.y += gravity * delta
		is_jumping = true
	else:
		is_jumping = false

	# Handle jump.
	if Input.is_action_just_pressed("jump") and is_on_floor():
		velocity.y = JUMP_VELOCITY
		is_jumping = true

	# Get the input direction and handle the movement/deceleration.
	var direction = Input.get_axis("move_left", "move_right")
	if direction != 0:
		velocity.x = direction * SPEED
		# تدوير الشخصية حسب اتجاه الحركة
		if direction > 0:
			scale.x = 1
		else:
			scale.x = -1
	else:
		velocity.x = move_toward(velocity.x, 0, SPEED)

	move_and_slide()
	
	# التحقق من الحدود
	check_boundaries()

func check_boundaries():
	# إعادة اللاعب إلى الموقع الأولي إذا سقط
	if position.y > 700:
		position = Vector2(100, 500)
		velocity = Vector2.ZERO

func _on_coin_collected():
	# استدعاء دالة جمع العملة في المشهد الرئيسي
	var main_scene = get_tree().current_scene
	if main_scene.has_method("collect_coin"):
		main_scene.collect_coin()
