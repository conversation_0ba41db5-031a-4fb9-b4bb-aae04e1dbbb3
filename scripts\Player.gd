extends CharacterBody2D

const SPEED = 200.0
const JUMP_VELOCITY = -400.0
var gravity = 980

func _ready():
	print("Player ready!")

func _physics_process(delta):
	# Add the gravity.
	if not is_on_floor():
		velocity.y += gravity * delta

	# Handle jump.
	if Input.is_action_just_pressed("jump") and is_on_floor():
		velocity.y = JUMP_VELOCITY
		# تشغيل صوت القفز
		var main_scene = get_tree().current_scene
		if main_scene.has_method("play_jump_sound"):
			main_scene.play_jump_sound()

	# Get the input direction and handle the movement/deceleration.
	var direction = Input.get_axis("move_left", "move_right")
	if direction != 0:
		velocity.x = direction * SPEED
	else:
		velocity.x = move_toward(velocity.x, 0, SPEED)

	move_and_slide()

	# التحقق من الحدود
	if position.y > 700:
		position = Vector2(100, 500)
		velocity = Vector2.ZERO
