# إعداد اللعبة لأندرويد - Android Setup

## 📱 متطلبات التصدير لأندرويد

### 1. تحميل Android SDK
- حمل Android Studio من: https://developer.android.com/studio
- أو حمل Command Line Tools فقط
- تأكد من تثبيت Android SDK Platform 33 أو أحدث

### 2. <PERSON><PERSON><PERSON><PERSON>ot للأندرويد
1. افت<PERSON> Godot
2. <PERSON><PERSON><PERSON><PERSON> إلى Editor → Editor Settings
3. في قسم Export → Android:
   - Android SDK Path: مسار Android SDK
   - Debug Keystore: سيتم إنشاؤه تلقائياً

### 3. تحميل قوالب التصدير
1. في Godot، اذهب إلى Project → Export
2. اضغط "Add..." واختر "Android"
3. اضغط "Manage Export Templates"
4. حمل القوالب للإصدار الحالي من Godot

## 🎮 تحسينات للعب على الهاتف

### التحكم باللمس
اللعبة الحالية تستخدم لوحة المفاتيح، لكن يمكن إضافة أزرار لمس:

1. **أزرار افتراضية**: Godot يضيف أزرار لمس تلقائياً
2. **أزرار مخصصة**: يمكن إضافة واجهة لمس مخصصة

### إعدادات الأداء
- الدقة: 1080x1920 (عمودي) أو 1920x1080 (أفقي)
- معدل الإطارات: 60 FPS
- ضغط الصور: لتوفير مساحة

## 📋 خطوات التصدير

### 1. إعداد المشروع
```
Project Settings → Rendering → Textures:
- Canvas Textures → Default Texture Filter: Linear
```

### 2. إعدادات التصدير
```
Project → Export → Android:
- Package Name: com.yourname.myfirstgame
- Version Name: 1.0
- Version Code: 1
- Min SDK: 21 (Android 5.0)
- Target SDK: 33
```

### 3. الأذونات المطلوبة
- INTERNET (إذا كنت تريد إضافة ميزات أونلاين لاحقاً)
- WRITE_EXTERNAL_STORAGE (لحفظ البيانات)

### 4. التصدير النهائي
1. اضغط "Export Project"
2. اختر مكان حفظ ملف APK
3. انتظر انتهاء عملية البناء

## 🎯 تحسينات إضافية للهاتف

### حجم الشاشة
- اللعبة ستتكيف تلقائياً مع أحجام الشاشات المختلفة
- الواجهة مصممة لتعمل على جميع الأحجام

### الأداء
- اللعبة خفيفة وستعمل على معظم الهواتف
- استهلاك بطارية منخفض
- حجم ملف صغير (أقل من 50 MB)

### التحكم
- يمكن اللعب بالأزرار الافتراضية
- أو إضافة أزرار لمس مخصصة

## 🚀 نصائح للنشر

### Google Play Store
1. إنشاء حساب مطور (25$ رسوم لمرة واحدة)
2. رفع ملف AAB بدلاً من APK
3. إضافة وصف ولقطات شاشة
4. تحديد الفئة العمرية والتصنيف

### متاجر أخرى
- Amazon Appstore
- Samsung Galaxy Store
- F-Droid (للتطبيقات المفتوحة المصدر)

## 📱 اختبار اللعبة

### على الكمبيوتر
- استخدم محاكي أندرويد (Android Studio Emulator)
- أو Bluestacks

### على الهاتف
- فعل "Developer Options" و "USB Debugging"
- اربط الهاتف بالكمبيوتر
- صدر مباشرة للهاتف من Godot

## 🎮 اللعبة جاهزة للأندرويد!

اللعبة مصممة بطريقة تجعلها تعمل بشكل ممتاز على أندرويد:
- واجهة مستخدم متجاوبة
- أداء محسن
- حجم صغير
- تحكم بسيط
