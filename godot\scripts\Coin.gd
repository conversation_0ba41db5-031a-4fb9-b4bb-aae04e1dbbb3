extends Area2D

var rotation_speed = 2.0
var bounce_height = 5.0
var bounce_speed = 3.0
var original_y

func _ready():
	original_y = position.y

func _process(delta):
	# تدوير العملة
	rotation += rotation_speed * delta
	
	# حركة ارتداد بسيطة
	position.y = original_y + sin(Time.get_ticks_msec() / 1000.0 * bounce_speed) * bounce_height

func _on_body_entered(body):
	if body.name == "Player":
		# تشغيل صوت جمع العملة (إذا كان متوفراً)
		collect_coin()
		
		# إخفاء العملة
		queue_free()

func collect_coin():
	# إرسال إشارة إلى المشهد الرئيسي
	var main_scene = get_tree().current_scene
	if main_scene.has_method("collect_coin"):
		main_scene.collect_coin()
	
	print("Coin collected!")
