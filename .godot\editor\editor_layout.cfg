[docks]

dock_1_selected_tab_idx=0
dock_2_selected_tab_idx=0
dock_3_selected_tab_idx=1
dock_4_selected_tab_idx=0
dock_5_selected_tab_idx=0
dock_6_selected_tab_idx=0
dock_7_selected_tab_idx=0
dock_8_selected_tab_idx=0
dock_floating={}
dock_split_2=0
dock_split_3=0
dock_hsplit_1=0
dock_hsplit_2=270
dock_hsplit_3=-270
dock_hsplit_4=0
dock_filesystem_split=0
dock_filesystem_display_mode=0
dock_filesystem_file_sort=0
dock_filesystem_file_list_display_mode=1
dock_filesystem_selected_paths=PackedStringArray("res://")
dock_filesystem_uncollapsed_paths=PackedStringArray("Favorites", "res://")
dock_3="Scene,Import"
dock_4="FileSystem"
dock_5="Inspector,Node,History"

[EditorNode]

open_scenes=PackedStringArray("res://scenes/Main.tscn")
current_scene="res://scenes/Main.tscn"
center_split_offset=0
selected_default_debugger_tab_idx=0
selected_main_editor_idx=2
selected_bottom_panel_item=1

[ScriptEditor]

open_scripts=["res://scripts/Coin.gd", "res://scripts/Main.gd"]
selected_script="res://scripts/Main.gd"
open_help=[]
script_split_offset=70
list_split_offset=0

[ShaderEditor]

open_shaders=[]
split_offset=0
selected_shader=""
