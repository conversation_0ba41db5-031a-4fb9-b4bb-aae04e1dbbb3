[gd_scene load_steps=11 format=3]

[ext_resource type="Script" path="res://scripts/Main.gd" id="1"]
[ext_resource type="PackedScene" path="res://scenes/Player.tscn" id="2"]
[ext_resource type="PackedScene" path="res://scenes/Coin.tscn" id="3"]
[ext_resource type="PackedScene" path="res://scenes/UI.tscn" id="4"]
[ext_resource type="PackedScene" path="res://scenes/Enemy.tscn" id="5"]
[ext_resource type="PackedScene" path="res://scenes/Spike.tscn" id="6"]
[ext_resource type="PackedScene" path="res://scenes/AudioManager.tscn" id="7"]
[ext_resource type="PackedScene" path="res://scenes/TouchControls.tscn" id="8"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(1024, 40)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_2"]
size = Vector2(200, 20)

[node name="Level2" type="Node2D"]
script = ExtResource("1")

[node name="Background" type="ColorRect" parent="."]
offset_right = 1024.0
offset_bottom = 600.0
color = Color(0.8, 0.6, 0.9, 1)

[node name="Ground" type="StaticBody2D" parent="."]
position = Vector2(512, 580)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Ground"]
shape = SubResource("RectangleShape2D_1")

[node name="ColorRect" type="ColorRect" parent="Ground"]
offset_left = -512.0
offset_top = -20.0
offset_right = 512.0
offset_bottom = 20.0
color = Color(0.4, 0.2, 0.1, 1)

[node name="Player" parent="." instance=ExtResource("2")]
position = Vector2(100, 500)

[node name="Camera2D" type="Camera2D" parent="."]
position = Vector2(512, 300)

[node name="Platform1" type="StaticBody2D" parent="."]
position = Vector2(250, 480)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Platform1"]
shape = SubResource("RectangleShape2D_2")

[node name="ColorRect" type="ColorRect" parent="Platform1"]
offset_left = -100.0
offset_top = -10.0
offset_right = 100.0
offset_bottom = 10.0
color = Color(0.7, 0.3, 0.1, 1)

[node name="Platform2" type="StaticBody2D" parent="."]
position = Vector2(500, 400)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Platform2"]
shape = SubResource("RectangleShape2D_2")

[node name="ColorRect" type="ColorRect" parent="Platform2"]
offset_left = -100.0
offset_top = -10.0
offset_right = 100.0
offset_bottom = 10.0
color = Color(0.7, 0.3, 0.1, 1)

[node name="Platform3" type="StaticBody2D" parent="."]
position = Vector2(750, 320)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Platform3"]
shape = SubResource("RectangleShape2D_2")

[node name="ColorRect" type="ColorRect" parent="Platform3"]
offset_left = -100.0
offset_top = -10.0
offset_right = 100.0
offset_bottom = 10.0
color = Color(0.7, 0.3, 0.1, 1)

[node name="Coins" type="Node2D" parent="."]

[node name="Coin1" parent="Coins" instance=ExtResource("3")]
position = Vector2(250, 430)

[node name="Coin2" parent="Coins" instance=ExtResource("3")]
position = Vector2(500, 350)

[node name="Coin3" parent="Coins" instance=ExtResource("3")]
position = Vector2(750, 270)

[node name="Coin4" parent="Coins" instance=ExtResource("3")]
position = Vector2(900, 520)

[node name="Enemies" type="Node2D" parent="."]

[node name="Enemy1" parent="Enemies" instance=ExtResource("5")]
position = Vector2(350, 540)

[node name="Enemy2" parent="Enemies" instance=ExtResource("5")]
position = Vector2(650, 540)

[node name="Hazards" type="Node2D" parent="."]

[node name="Spike1" parent="Hazards" instance=ExtResource("6")]
position = Vector2(400, 568)

[node name="Spike2" parent="Hazards" instance=ExtResource("6")]
position = Vector2(432, 568)

[node name="AudioManager" parent="." instance=ExtResource("7")]

[node name="UI" parent="." instance=ExtResource("4")]

[node name="TouchControls" parent="." instance=ExtResource("8")]
