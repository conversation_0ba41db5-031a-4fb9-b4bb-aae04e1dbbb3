extends Node

var current_level = 1
var max_levels = 3

var level_scenes = [
	"res://scenes/Level1.tscn",
	"res://scenes/Level2.tscn", 
	"res://scenes/Level3.tscn"
]

func _ready():
	print("Level Manager ready!")

func load_level(level_number):
	if level_number <= max_levels and level_number > 0:
		current_level = level_number
		var level_path = level_scenes[level_number - 1]
		print("Loading level: ", level_number)
		get_tree().change_scene_to_file(level_path)
	else:
		print("Invalid level number: ", level_number)

func next_level():
	if current_level < max_levels:
		load_level(current_level + 1)
	else:
		print("Game completed!")
		# يمكن إضافة شاشة النهاية هنا
		show_game_complete()

func restart_current_level():
	load_level(current_level)

func show_game_complete():
	print("Congratulations! You completed all levels!")
	# يمكن إضافة شاشة النهاية هنا لاحقاً
