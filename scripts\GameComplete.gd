extends Control

@onready var score_label = $VBoxContainer/ScoreLabel

var final_score = 0

func _ready():
	print("Game Complete screen loaded!")
	# يمكن تمرير النقاط النهائية هنا
	update_score(final_score)

func update_score(score):
	final_score = score
	score_label.text = "Final Score: " + str(score)

func _on_play_again_button_pressed():
	print("Playing again...")
	get_tree().change_scene_to_file("res://scenes/Level1.tscn")

func _on_main_menu_button_pressed():
	print("Going to main menu...")
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func _on_quit_button_pressed():
	print("Quitting game...")
	get_tree().quit()
