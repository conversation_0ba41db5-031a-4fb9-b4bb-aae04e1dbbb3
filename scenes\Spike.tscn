[gd_scene load_steps=3 format=3]

[ext_resource type="Script" path="res://scripts/Spike.gd" id="1"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(32, 16)

[node name="Spike" type="Area2D"]
script = ExtResource("1")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_1")

[node name="Polygon2D" type="Polygon2D" parent="."]
color = Color(0.5, 0.5, 0.5, 1)
polygon = PackedVector2Array(-16, 8, -8, -8, 0, 8, 8, -8, 16, 8)

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
