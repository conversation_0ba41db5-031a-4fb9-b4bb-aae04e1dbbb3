extends Node2D

@onready var particles = $CPUParticles2D
@onready var score_popup = $ScorePopup

func _ready():
	# تشغيل المؤثرات
	if particles:
		particles.emitting = true
	
	# تحريك النص للأعلى
	if score_popup:
		var tween = create_tween()
		tween.parallel().tween_property(score_popup, "position", Vector2(0, -50), 1.0)
		tween.parallel().tween_property(score_popup, "modulate:a", 0.0, 1.0)
	
	# حذف المؤثر بعد ثانيتين
	await get_tree().create_timer(2.0).timeout
	queue_free()

func show_effect():
	if particles:
		particles.emitting = true
