extends Node2D

var score = 0
var coins_collected = 0
var total_coins = 0  # سيتم حسابها تلقائياً
var current_level = 1

@onready var player = $Player
@onready var camera = $Camera2D
@onready var ui = $UI
@onready var audio_manager = $AudioManager

func _ready():
	print("Game started!")

	# حساب عدد العملات في المستوى
	count_coins()

	if player:
		print("Player found!")
	if camera:
		print("Camera found!")
		# تعيين الكاميرا كالكاميرا الحالية
		camera.make_current()

	# تحديث واجهة المستخدم
	if ui:
		ui.update_score(score)
		ui.update_coins(coins_collected)

func count_coins():
	var coins_node = get_node_or_null("Coins")
	if coins_node:
		total_coins = coins_node.get_child_count()
		print("Total coins in level: ", total_coins)

func _process(delta):
	# تحديث موقع الكاميرا لتتبع اللاعب
	if player and camera:
		# تحريك الكاميرا بسلاسة
		var target_x = player.position.x
		var target_y = player.position.y - 100

		camera.position.x = lerp(camera.position.x, target_x, 2.0 * delta)
		camera.position.y = lerp(camera.position.y, target_y, 2.0 * delta)

		# الحد من حركة الكاميرا في الحدود
		camera.position.x = max(camera.position.x, 512)
		camera.position.y = clamp(camera.position.y, 200, 400)

func add_score(points):
	score += points
	if ui:
		ui.update_score(score)
	print("Score: ", score)

func collect_coin():
	coins_collected += 1
	add_score(10)
	if ui:
		ui.update_coins(coins_collected)
	if audio_manager:
		audio_manager.play_coin_sound()
	print("Coins collected: ", coins_collected)

	# التحقق من جمع جميع العملات
	if coins_collected >= total_coins:
		print("Congratulations! You collected all coins!")
		show_victory_message()

func show_victory_message():
	print("Level completed! Score: ", score)
	# الانتقال للمستوى التالي بعد ثانيتين
	await get_tree().create_timer(2.0).timeout
	next_level()

func next_level():
	# تحديد المستوى التالي
	var next_level_scene = ""
	if get_tree().current_scene.name == "Level1":
		next_level_scene = "res://scenes/Level2.tscn"
	elif get_tree().current_scene.name == "Level2":
		next_level_scene = "res://scenes/Level3.tscn"
	else:
		print("Game completed! Final Score: ", score)
		return

	print("Loading next level...")
	get_tree().change_scene_to_file(next_level_scene)

func lose_points(points):
	score = max(0, score - points)  # لا تجعل النقاط سالبة
	if ui:
		ui.update_score(score)
	if audio_manager:
		audio_manager.play_hit_sound()
	print("Lost points! Current score: ", score)

func play_jump_sound():
	if audio_manager:
		audio_manager.play_jump_sound()
