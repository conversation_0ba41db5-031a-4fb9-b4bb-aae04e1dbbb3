extends Node2D

var score = 0
var coins_collected = 0
var total_coins = 5

@onready var player = $Player
@onready var camera = $Camera2D
@onready var ui = $UI

func _ready():
	# تحديث الكاميرا لتتبع اللاعب
	if player:
		camera.position = player.position

	# تحديث واجهة المستخدم
	if ui:
		ui.update_score(score)
		ui.update_coins(coins_collected)

func _process(delta):
	# تحديث موقع الكاميرا لتتبع اللاعب
	if player:
		camera.position.x = player.position.x
		# الحفاظ على الكاميرا في حدود معقولة
		camera.position.x = clamp(camera.position.x, 512, 512)

func add_score(points):
	score += points
	if ui:
		ui.update_score(score)
	print("Score: ", score)

func collect_coin():
	coins_collected += 1
	add_score(10)
	if ui:
		ui.update_coins(coins_collected)
	print("Coins collected: ", coins_collected)

	# التحقق من جمع جميع العملات
	if coins_collected >= total_coins:
		print("Congratulations! You collected all coins!")
		show_victory_message()

func show_victory_message():
	print("Victory! Final Score: ", score)
