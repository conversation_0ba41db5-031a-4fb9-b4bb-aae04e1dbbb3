# دليل تصدير اللعبة - Export Guide

## 📱 تصدير للأندرويد

### الخطوة 1: إعداد البيئة
1. **تحميل Android Studio**:
   - اذهب إلى: https://developer.android.com/studio
   - حمل وثبت Android Studio
   - افتح SDK Manager وحمل:
     - Android SDK Platform 33
     - Android SDK Build-Tools
     - Android SDK Command-line Tools

2. **إعد<PERSON>**:
   - افتح Godot
   - اذهب إلى Editor → Editor Settings
   - في Export → Android:
     - Android SDK Path: `C:\Users\<USER>\AppData\Local\Android\Sdk`
     - Debug Keystore: سيتم إنشاؤه تلقائياً

### الخطوة 2: تحميل قوالب التصدير
1. في Godot، اذهب إلى Project → Export
2. اضغط "Add..." واختر "Android"
3. اضغط "Manage Export Templates"
4. اضغط "Download and Install" للإصدار الحالي

### الخطوة 3: إعداد التصدير
```
Project → Export → Android:

Basic Settings:
- Export Path: MyFirstGame.apk
- Package Name: com.yourname.myfirstgame
- Version Name: 1.0
- Version Code: 1

Architecture:
- Export Format: APK
- Architectures: arm64-v8a, armeabi-v7a

Screen:
- Orientation: Portrait أو Landscape
- Support Large Heap: تفعيل
- Support 32 Bit: تفعيل

Permissions:
- INTERNET: إذا كنت تريد ميزات أونلاين
- WRITE_EXTERNAL_STORAGE: لحفظ البيانات
```

### الخطوة 4: التصدير
1. اضغط "Export Project"
2. اختر مكان حفظ ملف APK
3. انتظر انتهاء البناء
4. ستحصل على ملف APK جاهز للتثبيت

## 💻 تصدير للويندوز

### إعداد بسيط:
1. Project → Export → Add → Windows Desktop
2. Export Path: MyFirstGame.exe
3. اضغط "Export Project"

## 🌐 تصدير للويب (HTML5)

### للعب في المتصفح:
1. Project → Export → Add → Web
2. Export Path: index.html
3. اضغط "Export Project"
4. ارفع الملفات على خادم ويب

## 🍎 تصدير لـ iOS

### متطلبات:
- جهاز Mac
- Xcode مثبت
- حساب Apple Developer (99$ سنوياً)

### الخطوات:
1. Project → Export → Add → iOS
2. اتبع نفس خطوات أندرويد
3. ستحتاج لفتح المشروع في Xcode للتوقيع

## 🎮 تحسينات للأداء

### لأندرويد:
```gdscript
# في project.godot
[rendering]
renderer/rendering_method="mobile"
textures/vram_compression/import_etc2_astc=true

[audio]
driver/enable_input=false
```

### تقليل حجم الملف:
- ضغط الصور
- إزالة الملفات غير المستخدمة
- استخدام تنسيقات محسنة

## 📊 إحصائيات التصدير المتوقعة

### حجم الملفات:
- **Android APK**: ~25-40 MB
- **Windows EXE**: ~35-50 MB
- **Web**: ~20-30 MB

### متطلبات النظام:
- **Android**: 5.0+ (API 21)
- **RAM**: 1GB+
- **Storage**: 50MB

## 🚀 نشر اللعبة

### Google Play Store:
1. إنشاء حساب مطور (25$ لمرة واحدة)
2. رفع ملف AAB (أفضل من APK)
3. إضافة وصف ولقطات شاشة
4. تحديد التصنيف والفئة العمرية

### متاجر أخرى:
- **itch.io**: مجاني للألعاب المستقلة
- **Steam**: للألعاب التجارية
- **Microsoft Store**: للويندوز
- **App Store**: لـ iOS

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:
1. **"Android SDK not found"**:
   - تأكد من مسار SDK صحيح
   - أعد تشغيل Godot

2. **"Export templates not found"**:
   - حمل القوالب من Project → Export

3. **"APK لا يعمل"**:
   - تأكد من تفعيل "Unknown Sources"
   - جرب على أجهزة مختلفة

## ✅ اللعبة جاهزة للنشر!

اللعبة الآن مُحسنة للعمل على:
- ✅ أندرويد (مع أزرار لمس)
- ✅ ويندوز (لوحة مفاتيح)
- ✅ ويب (متصفح)
- ✅ iOS (مع بعض الإعداد الإضافي)

جميع الميزات تعمل على جميع المنصات:
- نظام النقاط والحفظ
- المؤثرات الصوتية والبصرية
- واجهة المستخدم المتجاوبة
- التحكم المتكيف (لمس أو لوحة مفاتيح)
