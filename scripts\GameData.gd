extends Node

# نظام حفظ البيانات
var save_file_path = "user://game_save.dat"
var high_score = 0
var levels_completed = 0

func _ready():
	load_game_data()

func save_game_data():
	var file = FileAccess.open(save_file_path, FileAccess.WRITE)
	if file:
		var save_data = {
			"high_score": high_score,
			"levels_completed": levels_completed
		}
		file.store_string(JSON.stringify(save_data))
		file.close()
		print("Game data saved!")

func load_game_data():
	if FileAccess.file_exists(save_file_path):
		var file = FileAccess.open(save_file_path, FileAccess.READ)
		if file:
			var json_string = file.get_as_text()
			file.close()
			
			var json = JSON.new()
			var parse_result = json.parse(json_string)
			
			if parse_result == OK:
				var save_data = json.data
				high_score = save_data.get("high_score", 0)
				levels_completed = save_data.get("levels_completed", 0)
				print("Game data loaded! High Score: ", high_score)
			else:
				print("Error parsing save file")
	else:
		print("No save file found, starting fresh")

func update_high_score(new_score):
	if new_score > high_score:
		high_score = new_score
		save_game_data()
		return true
	return false

func complete_level(level_number):
	if level_number > levels_completed:
		levels_completed = level_number
		save_game_data()
