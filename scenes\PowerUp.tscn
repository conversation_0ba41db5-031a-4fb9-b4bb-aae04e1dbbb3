[gd_scene load_steps=3 format=3]

[ext_resource type="Script" path="res://scripts/PowerUp.gd" id="1"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 16.0

[node name="PowerUp" type="Area2D"]
script = ExtResource("1")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_1")

[node name="ColorRect" type="ColorRect" parent="."]
offset_left = -16.0
offset_top = -16.0
offset_right = 16.0
offset_bottom = 16.0
color = Color(0, 1, 0, 1)

[node name="Plus" type="Label" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -8.0
offset_top = -12.0
offset_right = 8.0
offset_bottom = 12.0
text = "+"
horizontal_alignment = 1
vertical_alignment = 1

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
