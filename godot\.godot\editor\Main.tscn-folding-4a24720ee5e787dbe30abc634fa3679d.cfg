[folding]

node_unfolds=[NodePath("."), PackedStringArray("Transform"), NodePath("Background"), PackedStringArray("Layout"), NodePath("Ground"), PackedStringArray("Transform"), NodePath("Ground/CollisionShape2D"), PackedStringArray("Transform"), NodePath("Ground/ColorRect"), PackedStringArray("Layout"), NodePath("Platform1"), PackedStringArray("Transform"), NodePath("Platform1/CollisionShape2D"), PackedStringArray("Transform"), NodePath("Platform1/ColorRect"), PackedStringArray("Layout"), NodePath("Platform2"), PackedStringArray("Transform"), NodePath("Platform2/CollisionShape2D"), PackedStringArray("Transform"), NodePath("Platform2/ColorRect"), PackedStringArray("Layout"), NodePath("Platform3"), PackedStringArray("Transform"), NodePath("Platform3/CollisionShape2D"), PackedStringArray("Transform"), NodePath("Platform3/ColorRect"), PackedStringArray("Layout"), NodePath("Player"), PackedStringArray("Transform"), NodePath("Camera2D"), PackedStringArray("Transform"), NodePath("Coins/Coin1"), PackedStringArray("Transform"), NodePath("Coins/Coin2"), PackedStringArray("Transform"), NodePath("Coins/Coin3"), PackedStringArray("Transform"), NodePath("Coins/Coin4"), PackedStringArray("Transform"), NodePath("Coins/Coin5"), PackedStringArray("Transform")]
resource_unfolds=["res://scenes/Main.tscn::RectangleShape2D_1hdqp", PackedStringArray("Resource")]
nodes_folded=[]
