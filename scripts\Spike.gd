extends Area2D

func _on_body_entered(body):
	if body.name == "Player":
		print("Player hit spikes!")
		# إعادة اللاعب للموقع الأولي
		body.position = Vector2(100, 500)
		body.velocity = Vector2.ZERO

		# تقليل النقاط والحياة أكثر من العدو
		var main_scene = get_tree().current_scene
		if main_scene.has_method("lose_points"):
			main_scene.lose_points(10)
		if main_scene.has_method("lose_life"):
			main_scene.lose_life()
