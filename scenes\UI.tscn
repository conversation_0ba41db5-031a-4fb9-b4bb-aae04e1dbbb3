[gd_scene load_steps=2 format=3]

[ext_resource type="Script" path="res://scripts/UI.gd" id="1_4hdqp"]

[node name="UI" type="CanvasLayer"]
script = ExtResource("1_4hdqp")

[node name="ScoreLabel" type="Label" parent="."]
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -60.0
offset_right = 200.0
offset_bottom = -20.0
text = "Score: 0"

[node name="CoinsLabel" type="Label" parent="."]
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -100.0
offset_right = 200.0
offset_bottom = -60.0
text = "Coins: 0"

[node name="TimeLabel" type="Label" parent="."]
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -140.0
offset_right = 200.0
offset_bottom = -100.0
text = "Time: 0s"

[node name="LivesLabel" type="Label" parent="."]
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -200.0
offset_top = -60.0
offset_right = -20.0
offset_bottom = -20.0
text = "Lives: ♥♥♥"
horizontal_alignment = 2

[node name="InstructionsLabel" type="Label" parent="."]
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -300.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = 100.0
text = "Use A/D or Arrow Keys to move
Space or Up Arrow to jump
Collect all coins!"
horizontal_alignment = 2
