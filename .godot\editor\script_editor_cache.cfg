[res://scripts/Main.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Coin.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 34,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 15,
"scroll_position": 5.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
