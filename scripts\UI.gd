extends <PERSON><PERSON><PERSON><PERSON><PERSON>

@onready var score_label = $ScoreLabel
@onready var coins_label = $CoinsLabel
@onready var time_label = $TimeLabel
@onready var lives_label = $LivesLabel

func _ready():
	print("UI ready!")

func update_score(score):
	if score_label:
		score_label.text = "Score: " + str(score)

func update_coins(coins):
	if coins_label:
		coins_label.text = "Coins: " + str(coins)

func update_time(time):
	if time_label:
		time_label.text = "Time: " + str(int(time)) + "s"

func update_lives(lives):
	if lives_label:
		var hearts = ""
		for i in range(lives):
			hearts += "♥"
		lives_label.text = "Lives: " + hearts
