extends <PERSON>vas<PERSON><PERSON><PERSON>

@onready var score_label = $ScoreLabel
@onready var coins_label = $CoinsLabel

func _ready():
	# الاتصال بالمشهد الرئيسي لتحديث النقاط
	var main_scene = get_tree().current_scene
	if main_scene:
		main_scene.connect("score_updated", _on_score_updated)
		main_scene.connect("coins_updated", _on_coins_updated)

func _on_score_updated(new_score):
	score_label.text = "Score: " + str(new_score)

func _on_coins_updated(coins_count):
	coins_label.text = "Coins: " + str(coins_count)

func update_score(score):
	score_label.text = "Score: " + str(score)

func update_coins(coins):
	coins_label.text = "Coins: " + str(coins)
