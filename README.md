# My First Game - لعبتي الأولى

لعبة منصات بسيطة ثنائية الأبعاد تم إنشاؤها باستخدام محرك Godot.

## وصف اللعبة

هذه لعبة منصات بسيطة حيث يتحكم اللاعب في شخصية حمراء صغيرة ويحاول جمع جميع العملات الذهبية المنتشرة في المستوى.

## طريقة اللعب

### التحكم:
- **A** أو **السهم الأيسر**: الحركة يساراً
- **D** أو **السهم الأيمن**: الحركة يميناً  
- **مسطرة المسافة** أو **السهم العلوي**: القفز

### الهدف:
- اجمع جميع العملات الذهبية (5 عملات)
- تجنب السقوط من المنصات
- احصل على أعلى نقاط ممكنة

## ميزات اللعبة

- **حركة سلسة**: نظام حركة وقفز متجاوب
- **فيزياء واقعية**: جاذبية وتصادمات طبيعية
- **عملات متحركة**: عملات ذهبية تدور وترتد
- **نظام نقاط**: احصل على 10 نقاط لكل عملة
- **واجهة مستخدم**: عرض النقاط والعملات المجمعة
- **إعادة تعيين تلقائية**: عودة للموقع الأولي عند السقوط

## بنية المشروع

```
├── project.godot          # ملف إعدادات المشروع
├── icon.svg              # أيقونة المشروع
├── scenes/               # مجلد المشاهد
│   ├── Main.tscn        # المشهد الرئيسي
│   ├── Player.tscn      # مشهد اللاعب
│   ├── Coin.tscn        # مشهد العملة
│   └── UI.tscn          # واجهة المستخدم
├── scripts/              # مجلد السكريبتات
│   ├── Main.gd          # سكريبت المشهد الرئيسي
│   ├── Player.gd        # سكريبت اللاعب
│   ├── Coin.gd          # سكريبت العملة
│   └── UI.gd            # سكريبت واجهة المستخدم
└── assets/               # مجلد الأصول
    ├── textures/        # الصور والرسوم
    └── sounds/          # الأصوات
```

## كيفية تشغيل اللعبة

1. تأكد من تثبيت محرك Godot (الإصدار 4.2 أو أحدث)
2. افتح المشروع في Godot
3. اضغط F5 أو زر "Play" لتشغيل اللعبة
4. استمتع باللعب!

## التطوير المستقبلي

يمكن إضافة المزيد من الميزات مثل:
- أعداء ومخاطر
- مستويات متعددة
- أصوات وموسيقى
- رسوم متحركة أفضل
- عناصر قوة خاصة
- نظام حفظ النقاط

## المطور

تم إنشاء هذه اللعبة كمثال تعليمي لاستخدام محرك Godot في تطوير الألعاب.
