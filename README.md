# My First Game - لعبتي الأولى

لعبة منصات كاملة ثنائية الأبعاد تم إنشاؤها باستخدام محرك Godot مع 3 مستويات مثيرة!

## وصف اللعبة

لعبة منصات مليئة بالتحدي تحتوي على 3 مستويات متدرجة الصعوبة. اجمع العملات، تجنب الأعداء والمخاطر، واحصل على أعلى نقاط ممكنة!

## طريقة اللعب

### التحكم:
- **A** أو **السهم الأيسر**: الحركة يساراً
- **D** أو **السهم الأيمن**: الحركة يميناً  
- **مسطرة المسافة** أو **السهم العلوي**: القفز

### الهدف:
- اجمع جميع العملات الذهبية (5 عملات)
- تجنب السقوط من المنصات
- احصل على أعلى نقاط ممكنة

## ميزات اللعبة

### 🎮 اللعب الأساسي
- **حركة سلسة**: نظام حركة وقفز متجاوب مع مؤثرات بصرية
- **فيزياء واقعية**: جاذبية وتصادمات طبيعية
- **3 مستويات**: مستويات متدرجة الصعوبة مع تصاميم مختلفة

### 💰 نظام النقاط والجوائز
- **عملات متحركة**: عملات ذهبية مع مؤثرات جمع رائعة
- **نظام نقاط متقدم**: 10 نقاط لكل عملة + مكافآت الوقت
- **أعلى نقاط**: حفظ تلقائي لأعلى نقاط محققة
- **مكافآت الوقت**: نقاط إضافية للإكمال السريع

### 🎯 التحدي والمخاطر
- **أعداء متحركة**: أعداء تتحرك في دوريات
- **مخاطر متنوعة**: أشواك وعقبات خطيرة
- **نظام الحياة**: 3 أرواح مع إمكانية الحصول على المزيد
- **نظام الوقت**: عداد وقت مع مكافآت السرعة

### 🎵 الصوت والمؤثرات
- **أصوات متنوعة**: أصوات للقفز وجمع العملات والإصابة
- **مؤثرات بصرية**: جسيمات للقفز وجمع العملات
- **واجهة مستخدم شاملة**: عرض النقاط والعملات والوقت والحياة

### 🏆 القوائم والتنقل
- **قائمة رئيسية**: مع عرض أعلى نقاط والتعليمات
- **شاشة إكمال**: عند إنهاء جميع المستويات
- **حفظ تلقائي**: للنقاط والتقدم

## بنية المشروع

```
├── project.godot          # ملف إعدادات المشروع
├── icon.svg              # أيقونة المشروع
├── scenes/               # مجلد المشاهد
│   ├── Main.tscn        # المشهد الرئيسي
│   ├── Player.tscn      # مشهد اللاعب
│   ├── Coin.tscn        # مشهد العملة
│   └── UI.tscn          # واجهة المستخدم
├── scripts/              # مجلد السكريبتات
│   ├── Main.gd          # سكريبت المشهد الرئيسي
│   ├── Player.gd        # سكريبت اللاعب
│   ├── Coin.gd          # سكريبت العملة
│   └── UI.gd            # سكريبت واجهة المستخدم
└── assets/               # مجلد الأصول
    ├── textures/        # الصور والرسوم
    └── sounds/          # الأصوات
```

## كيفية تشغيل اللعبة

1. تأكد من تثبيت محرك Godot (الإصدار 4.2 أو أحدث)
2. افتح المشروع في Godot
3. اضغط F5 أو زر "Play" لتشغيل اللعبة
4. استمتع باللعب!

## التطوير المستقبلي

يمكن إضافة المزيد من الميزات مثل:
- أعداء ومخاطر
- مستويات متعددة
- أصوات وموسيقى
- رسوم متحركة أفضل
- عناصر قوة خاصة
- نظام حفظ النقاط

## المطور

تم إنشاء هذه اللعبة كمثال تعليمي لاستخدام محرك Godot في تطوير الألعاب.
