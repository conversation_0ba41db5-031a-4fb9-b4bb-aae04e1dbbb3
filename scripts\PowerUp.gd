extends Area2D

var rotation_speed = 1.0
var float_height = 3.0
var float_speed = 2.0
var original_y

func _ready():
	original_y = position.y

func _process(delta):
	# تدوير القوة
	rotation += rotation_speed * delta
	
	# حركة طفو
	position.y = original_y + sin(Time.get_time() * float_speed) * float_height

func _on_body_entered(body):
	if body.name == "Player":
		print("PowerUp collected!")
		
		# إضافة حياة إضافية
		var main_scene = get_tree().current_scene
		if main_scene.has_method("add_life"):
			main_scene.add_life()
		
		# إخفاء القوة
		queue_free()
