[gd_scene load_steps=3 format=3]

[ext_resource type="Script" path="res://scripts/Coin.gd" id="1_3hdqp"]

[sub_resource type="CircleShape2D" id="CircleShape2D_3hdqp"]
radius = 12.0

[node name="Coin" type="Area2D"]
script = ExtResource("1_3hdqp")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_3hdqp")

[node name="ColorRect" type="ColorRect" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -12.0
offset_top = -12.0
offset_right = 12.0
offset_bottom = 12.0
color = Color(1, 0.843137, 0, 1)

[node name="InnerCircle" type="ColorRect" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -8.0
offset_top = -8.0
offset_right = 8.0
offset_bottom = 8.0
color = Color(1, 1, 0.6, 1)

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
