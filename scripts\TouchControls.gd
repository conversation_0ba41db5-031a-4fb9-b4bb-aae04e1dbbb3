extends CanvasLayer

var is_left_pressed = false
var is_right_pressed = false
var is_jump_pressed = false

func _ready():
	# إخفاء أزرار اللمس على الكمبيوتر
	if OS.get_name() != "Android" and OS.get_name() != "iOS":
		visible = false

func _on_left_button_pressed():
	is_left_pressed = true
	Input.action_press("move_left")

func _on_left_button_released():
	is_left_pressed = false
	Input.action_release("move_left")

func _on_right_button_pressed():
	is_right_pressed = true
	Input.action_press("move_right")

func _on_right_button_released():
	is_right_pressed = false
	Input.action_release("move_right")

func _on_jump_button_pressed():
	is_jump_pressed = true
	Input.action_press("jump")
	# تحرير الزر تلقائياً بعد وقت قصير للقفز
	await get_tree().create_timer(0.1).timeout
	Input.action_release("jump")
	is_jump_pressed = false
