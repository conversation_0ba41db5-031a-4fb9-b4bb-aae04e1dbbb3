[gd_scene load_steps=10 format=3]

[ext_resource type="Script" path="res://scripts/Main.gd" id="1"]
[ext_resource type="PackedScene" path="res://scenes/Player.tscn" id="2"]
[ext_resource type="PackedScene" path="res://scenes/Coin.tscn" id="3"]
[ext_resource type="PackedScene" path="res://scenes/UI.tscn" id="4"]
[ext_resource type="PackedScene" path="res://scenes/Enemy.tscn" id="5"]
[ext_resource type="PackedScene" path="res://scenes/Spike.tscn" id="6"]
[ext_resource type="PackedScene" path="res://scenes/AudioManager.tscn" id="7"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(1024, 40)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_2"]
size = Vector2(200, 20)

[node name="Level1" type="Node2D"]
script = ExtResource("1")

[node name="Background" type="ColorRect" parent="."]
offset_right = 1024.0
offset_bottom = 600.0
color = Color(0.529412, 0.808824, 0.921569, 1)

[node name="Ground" type="StaticBody2D" parent="."]
position = Vector2(512, 580)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Ground"]
shape = SubResource("RectangleShape2D_1")

[node name="ColorRect" type="ColorRect" parent="Ground"]
offset_left = -512.0
offset_top = -20.0
offset_right = 512.0
offset_bottom = 20.0
color = Color(0.133333, 0.545098, 0.133333, 1)

[node name="Player" parent="." instance=ExtResource("2")]
position = Vector2(100, 500)

[node name="Camera2D" type="Camera2D" parent="."]
position = Vector2(512, 300)

[node name="Platform1" type="StaticBody2D" parent="."]
position = Vector2(300, 450)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Platform1"]
shape = SubResource("RectangleShape2D_2")

[node name="ColorRect" type="ColorRect" parent="Platform1"]
offset_left = -100.0
offset_top = -10.0
offset_right = 100.0
offset_bottom = 10.0
color = Color(0.545098, 0.270588, 0.0745098, 1)

[node name="Platform2" type="StaticBody2D" parent="."]
position = Vector2(600, 350)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Platform2"]
shape = SubResource("RectangleShape2D_2")

[node name="ColorRect" type="ColorRect" parent="Platform2"]
offset_left = -100.0
offset_top = -10.0
offset_right = 100.0
offset_bottom = 10.0
color = Color(0.545098, 0.270588, 0.0745098, 1)

[node name="Coins" type="Node2D" parent="."]

[node name="Coin1" parent="Coins" instance=ExtResource("3")]
position = Vector2(300, 400)

[node name="Coin2" parent="Coins" instance=ExtResource("3")]
position = Vector2(600, 300)

[node name="Coin3" parent="Coins" instance=ExtResource("3")]
position = Vector2(150, 520)

[node name="Enemies" type="Node2D" parent="."]

[node name="Enemy1" parent="Enemies" instance=ExtResource("5")]
position = Vector2(450, 540)

[node name="AudioManager" parent="." instance=ExtResource("7")]

[node name="UI" parent="." instance=ExtResource("4")]
